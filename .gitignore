# Build artifacts
*.o
*.obj
*.lo
*.la
*.al
*.libs
*.a
*.lib
*.so
*.so.*
*.dylib
*.dll
*.exe
*.out
*.run

# CUDA cache and artifacts
*.ptx
*.cubin
*.cu.cpp
*.fatbin
*.gpu
*.ii
*.nvcc.*
*.linkinfo

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile

# Ninja
build/
.ninja_log
.ninja_deps
rules.ninja

# Visual Studio / MSBuild
*.vcxproj*
*.sln
*.sdf
*.opensdf
*.suo
*.user
*.ncb
*.ilk
*.pdb
*.ipch
*.tlog
*.log

# IDE/editor files
*.swp
*~
*.bak
*.tmp
*.DS_Store
.vscode/
.idea/

# Python bindings
__pycache__/
*.pyc
*.pyo
*.pyd
build/
dist/
*.egg-info/

# Misc
*.log
*.trace
core
vgcore.*
