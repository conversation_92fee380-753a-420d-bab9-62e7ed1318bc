"""
Bitonic Sort GPU Implementation - Main Script
Compares CPU vs GPU performance for bitonic sort algorithm
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import sys

# Try to import the compiled CUDA module
try:
    import bitonic_sort_cuda

    CUDA_AVAILABLE = True
    print("CUDA module loaded successfully!")
except ImportError as e:
    print(f"Warning: Could not import CUDA module: {e}")
    CUDA_AVAILABLE = False


def generate_random_array(size, seed=42):
    np.random.seed(seed)
    return np.random.uniform(0.0, 1000.0, size).astype(np.float32)


def validate_sort(original, sorted_array, reference_sorted):
    if not np.allclose(sorted_array, reference_sorted, rtol=1e-4):
        print("ERROR: Sorted array does not match reference!")
        print(f"First 10 elements of result: {sorted_array[:10]}")
        print(f"First 10 elements of reference: {reference_sorted[:10]}")
        return False
    if not np.all(sorted_array[:-1] <= sorted_array[1:]):
        print("ERROR: Sorted array is not monotonically increasing!")
        return False
    return True


def benchmark_numpy_sort(data, num_iterations=1000):
    total_time = 0.0
    result = None

    for _ in range(num_iterations):
        data_copy = data.copy()
        start_time = time.perf_counter()
        result = np.sort(data_copy)
        end_time = time.perf_counter()
        total_time += end_time - start_time

    average_time_ms = (total_time / num_iterations) * 1000
    return result, average_time_ms


def run_performance_comparison():
    # Run performance comparison between CPU and GPU implementations
    # Test sizes (powers of 2)
    test_sizes = [
        128,
        512,
        1024,
        2048,
        4096,
        8192,
        16384,
        32768,
        65536,
        131072,
        262144,
        524288,
        1048576,
    ]
    num_iterations = 1000

    results = {
        "sizes": [],
        "numpy_times": [],
        "gpu_shared_times": [],
        "cpu_times": [],
    }

    print("Running performance comparison...")
    print(f"{'Size':<10} {'NumPy (ms)':<12} {'GPU Shared (ms)':<16} {'CPU (ms)':<12}")

    for size in test_sizes:
        print(f"Testing size: {size}")

        # Generate test data
        data = generate_random_array(size)

        # NumPy reference sort
        numpy_result, numpy_time = benchmark_numpy_sort(data, num_iterations)

        results["sizes"].append(size)
        results["numpy_times"].append(numpy_time)

        if CUDA_AVAILABLE:
            try:
                # GPU shared memory sort
                gpu_shared_result, gpu_shared_time = (
                    bitonic_sort_cuda.bitonic_sort_gpu_shared_timed(
                        data, num_iterations
                    )
                )
                results["gpu_shared_times"].append(gpu_shared_time)

                # Validate GPU shared result
                if not validate_sort(data, gpu_shared_result, numpy_result):
                    print(f"GPU shared sort validation failed for size {size}")
                    sys.exit(1)

                # CPU sort (using std::sort)
                cpu_result, cpu_time = bitonic_sort_cuda.bitonic_sort_cpu_timed(
                    data, num_iterations
                )
                results["cpu_times"].append(cpu_time)

                # Validate CPU result
                if not validate_sort(data, cpu_result, numpy_result):
                    print(f"CPU sort validation failed for size {size}")
                    sys.exit(1)

                # Print results
                gpu_shared_str = (
                    f"{gpu_shared_time:.2f}" if gpu_shared_time is not None else "N/A"
                )
                print(
                    f"{size:<10} {numpy_time:<12.2f} {gpu_shared_str:<16} {cpu_time:<12.2f}"
                )

            except Exception as e:
                print(f"Error running CUDA functions for size {size}: {e}")
                results["gpu_shared_times"].append(None)
                results["cpu_times"].append(None)
        else:
            results["gpu_shared_times"].append(None)
            results["cpu_times"].append(None)
            print(f"{size:<10} {numpy_time:<12.2f} {'N/A':<12} {'N/A':<16} {'N/A':<12}")

    return results


def create_performance_plot(results):
    plt.figure(figsize=(12, 8))

    sizes = results["sizes"]

    # Plot NumPy times
    plt.plot(sizes, results["numpy_times"], "b-o", label="NumPy Sort", linewidth=2)

    if CUDA_AVAILABLE:
        # Plot GPU shared memory times
        gpu_shared_sizes = [
            s for s, t in zip(sizes, results["gpu_shared_times"]) if t is not None
        ]
        gpu_shared_times = [t for t in results["gpu_shared_times"] if t is not None]

        if gpu_shared_times:
            plt.plot(
                gpu_shared_sizes,
                gpu_shared_times,
                "g-^",
                label="GPU Shared Memory",
                linewidth=2,
            )

        # Plot CPU times
        cpu_sizes = [s for s, t in zip(sizes, results["cpu_times"]) if t is not None]
        cpu_times = [t for t in results["cpu_times"] if t is not None]

        if cpu_times:
            plt.plot(
                cpu_sizes, cpu_times, "m-d", label="CPU Sort (std::sort)", linewidth=2
            )

    plt.xlabel("Array Size")
    plt.ylabel("Time (milliseconds)")
    plt.title("Bitonic Sort Performance Comparison")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xscale("log", base=2)
    plt.yscale("log")

    # Save the plot
    plt.savefig("comparison_plot.png", dpi=300, bbox_inches="tight")
    print("\nPerformance plot saved as 'comparison_plot.png'")

    # Show the plot
    plt.show()


def main():
    print("Bitonic Sort GPU Implementation")

    if not CUDA_AVAILABLE:
        print("CUDA module not available")
        return

    # Run performance comparison
    results = run_performance_comparison()

    # Create performance plot
    create_performance_plot(results)

    print("\nAll tests completed successfully!")
    print("Results validated against NumPy sort.")


if __name__ == "__main__":
    main()
