#!/usr/bin/env python3
"""
Debug script to test bitonic sort implementation
"""

import numpy as np
import bitonic_sort_cuda


def test_small_array():
    """Test with a small array to see if basic functionality works"""
    print("Testing small array (size 8)...")
    data = np.array([8.0, 3.0, 5.0, 4.0, 7.0, 6.0, 1.0, 2.0], dtype=np.float32)
    print(f"Original: {data}")

    # Test shared memory version
    result_shared = bitonic_sort_cuda.bitonic_sort_gpu_shared(data.copy())
    print(f"GPU Shared: {result_shared}")

    # Test CPU version
    result_cpu = bitonic_sort_cuda.bitonic_sort_cpu(data.copy())
    print(f"CPU: {result_cpu}")

    # Reference
    reference = np.sort(data)
    print(f"NumPy: {reference}")

    print(f"Shared matches reference: {np.allclose(result_shared, reference)}")
    print(f"CPU matches reference: {np.allclose(result_cpu, reference)}")
    print()


def test_power_of_2():
    """Test with exact power of 2 sizes"""
    for size in [16, 32, 64, 128, 256, 512, 1024]:
        print(f"Testing size {size}...")
        data = np.random.uniform(0, 100, size).astype(np.float32)

        result_shared = bitonic_sort_cuda.bitonic_sort_gpu_shared(data.copy())
        reference = np.sort(data)

        if np.allclose(result_shared, reference):
            print(f"  ✓ Size {size} PASSED")
        else:
            print(f"  ✗ Size {size} FAILED")
            print(f"    First 5 result: {result_shared[:5]}")
            print(f"    First 5 reference: {reference[:5]}")
            break


def test_non_power_of_2():
    """Test with non-power of 2 sizes"""
    for size in [100, 200, 300, 500, 1000, 1500, 2000]:
        print(f"Testing size {size}...")
        # Use fixed seed for reproducible results
        np.random.seed(42)
        data = np.random.uniform(0, 100, size).astype(np.float32)

        result_shared = bitonic_sort_cuda.bitonic_sort_gpu_shared(data.copy())
        reference = np.sort(data)

        if np.allclose(result_shared, reference):
            print(f"  ✓ Size {size} PASSED")
        else:
            print(f"  ✗ Size {size} FAILED")
            print(f"    First 5 result: {result_shared[:5]}")
            print(f"    First 5 reference: {reference[:5]}")
            print(f"    Last 5 result: {result_shared[-5:]}")
            print(f"    Last 5 reference: {reference[-5:]}")

            # Check if it's just unsorted
            result_sorted = np.sort(result_shared)
            if np.allclose(result_sorted, reference):
                print(f"    Data is correct but not sorted!")
            else:
                print(f"    Data is corrupted!")
            break


def test_exact_1025():
    """Test the boundary case of 1025 elements"""
    print("Testing exact boundary cases...")

    # Test 1024 (single tile)
    np.random.seed(42)
    data_1024 = np.random.uniform(0, 100, 1024).astype(np.float32)
    result_1024 = bitonic_sort_cuda.bitonic_sort_gpu_shared(data_1024.copy())
    reference_1024 = np.sort(data_1024)
    print(f"Size 1024 - Matches reference: {np.allclose(result_1024, reference_1024)}")

    # Test 1025 (multi-tile)
    np.random.seed(42)
    data_1025 = np.random.uniform(0, 100, 1025).astype(np.float32)
    result_1025 = bitonic_sort_cuda.bitonic_sort_gpu_shared(data_1025.copy())
    reference_1025 = np.sort(data_1025)
    print(f"Size 1025 - Matches reference: {np.allclose(result_1025, reference_1025)}")

    # Test 2048 (exactly 2 tiles)
    np.random.seed(42)
    data_2048 = np.random.uniform(0, 100, 2048).astype(np.float32)
    result_2048 = bitonic_sort_cuda.bitonic_sort_gpu_shared(data_2048.copy())
    reference_2048 = np.sort(data_2048)
    print(f"Size 2048 - Matches reference: {np.allclose(result_2048, reference_2048)}")

    if not np.allclose(result_1025, reference_1025):
        print("1025 failed - checking data integrity...")
        result_sorted = np.sort(result_1025)
        if np.allclose(result_sorted, reference_1025):
            print("Data is correct but not sorted!")
        else:
            print("Data is corrupted!")

    print()


if __name__ == "__main__":
    print("Bitonic Sort Debug Test")
    print("=" * 40)

    test_small_array()
    test_power_of_2()
    test_exact_1025()
    test_non_power_of_2()
