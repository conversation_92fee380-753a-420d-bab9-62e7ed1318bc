# BitonicSortGPU

A high-performance implementation of the Bitonic Sort algorithm using CUDA with Python integration via pybind11. This project demonstrates GPU acceleration for parallel sorting with performance comparisons against CPU implementations.

## Features

- **CUDA Bitonic Sort**: Efficient GPU implementation using global memory
- **Shared Memory Optimization**: Optimized version for smaller arrays (≤1024 elements)
- **Python Integration**: Seamless Python-CUDA interface using pybind11
- **Performance Benchmarking**: Automated comparison between CPU and GPU implementations
- **Validation**: Results validated against NumPy's sort function
- **Visualization**: Performance comparison plots

## Requirements

- NVIDIA GPU with CUDA support
- CUDA Toolkit (11.0 or later)
- Python 3.6+
- C++ compiler with C++14 support

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set CUDA environment (if not already set):**
   ```bash
   export CUDA_HOME=/usr/local/cuda
   export PATH=$CUDA_HOME/bin:$PATH
   export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH
   ```

3. **Build the CUDA extension:**
   ```bash
   python setup.py build_ext --inplace
   ```

## Usage

**Run the complete benchmark and comparison:**
```bash
python3 main.py
```

This command will:
- Test multiple array sizes (1024 to 65536 elements)
- Compare NumPy, GPU, GPU shared memory, and CPU implementations
- Validate all results against NumPy sort
- Generate a performance comparison plot (`comparison_plot.png`)

## Project Structure

```
BitonicSortGPU/
├── bitonic_sort.cu              # CUDA kernel implementation
├── bitonic_sort_wrapper.cpp     # pybind11 Python-CUDA interface
├── setup.py                     # Build configuration
├── main.py                      # Main benchmark script
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## Algorithm Details

The Bitonic Sort is a comparison-based sorting algorithm that works well on parallel machines. It sorts sequences of length 2^k in O(log²n) time with O(n log²n) comparisons.

### Key Features:
- **Deterministic**: Fixed comparison pattern independent of data
- **Parallel-friendly**: Natural parallelization for GPU architectures
- **Memory-efficient**: In-place sorting with minimal memory overhead

### Implementation Variants:
1. **Global Memory**: Handles large arrays with global GPU memory
2. **Shared Memory**: Optimized for smaller arrays using fast shared memory
3. **CPU Reference**: Standard library sort for comparison

## Performance Characteristics

Expected performance improvements:
- **Small arrays (≤1024)**: Shared memory version shows best GPU performance
- **Large arrays (>1024)**: Global memory version scales well
- **Very large arrays**: GPU advantage becomes more pronounced

## Troubleshooting

**CUDA not found:**
- Ensure CUDA toolkit is installed
- Set CUDA_HOME environment variable
- Verify nvcc is in PATH

**Compilation errors:**
- Check C++ compiler supports C++14
- Verify pybind11 installation
- Ensure CUDA and Python versions are compatible

**Runtime errors:**
- Check GPU memory availability
- Verify CUDA drivers are up to date
- Ensure array sizes are reasonable for available GPU memory

## License

This project is for educational purposes as part of a GPU computing assignment.