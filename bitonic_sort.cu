#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <stdio.h>
#include <algorithm>
#include <cfloat>
#include <cstdlib>

#define CUDA_CHECK(call)                                               \
  do {                                                                 \
    cudaError_t err = call;                                            \
    if (err != cudaSuccess) {                                          \
      fprintf(stderr, "CUDA error at %s:%d: %s\n", __FILE__, __LINE__, \
              cudaGetErrorString(err));                                \
      exit(EXIT_FAILURE);                                              \
    }                                                                  \
  } while (0)

/**
 * CUDA kernel for bitonic sort using shared memory
 * Handles both single tile and multi-tile cases
 */
__global__ void bitonicSortShared(float* data, int n, int tile_size) {
  extern __shared__ float shared_data[];

  int tid = threadIdx.x;
  int bid = blockIdx.x;

  // Calculate tile boundaries
  int tile_start = bid * tile_size;
  int tile_end = min(tile_start + tile_size, n);

  // Load data into shared memory
  if (tid < tile_size) {
    if (tile_start + tid < tile_end) {
      shared_data[tid] = data[tile_start + tid];
    } else {
      shared_data[tid] = FLT_MAX;  // Padding
    }
  }
  __syncthreads();

  // For single tile, sort ascending. For multiple tiles, sort all ascending
  // first We'll handle the bitonic sequence creation in the merge phase
  bool tile_ascending = true;  // Always sort tiles in ascending order initially

  // Perform bitonic sort in shared memory
  for (int k = 2; k <= tile_size; k <<= 1) {
    for (int j = k >> 1; j > 0; j >>= 1) {
      int ixj = tid ^ j;
      if (ixj > tid && tid < tile_size && ixj < tile_size) {
        bool local_ascending =
            (k & tid) == 0;  // Standard bitonic sort direction
        if ((shared_data[tid] > shared_data[ixj]) == local_ascending) {
          // Swap elements
          float temp = shared_data[tid];
          shared_data[tid] = shared_data[ixj];
          shared_data[ixj] = temp;
        }
      }
      __syncthreads();
    }
  }

  // Write back to global memory
  if (tid < tile_size && tile_start + tid < tile_end) {
    data[tile_start + tid] = shared_data[tid];
  }
}

/**
 * CUDA kernel to reverse alternate tiles to create bitonic sequence
 */
__global__ void reverseTiles(float* data, int n, int tile_size) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;

  if (tid >= n)
    return;

  int tile_id = tid / tile_size;
  int pos_in_tile = tid % tile_size;

  // Only reverse odd-numbered tiles
  if ((tile_id % 2) == 1) {
    int tile_start = tile_id * tile_size;
    int tile_end = min(tile_start + tile_size, n);
    int partner_pos = tile_start + (tile_size - 1 - pos_in_tile);

    // Only swap if both positions are within bounds and we're in the first half
    if (partner_pos < tile_end && tid < partner_pos) {
      float temp = data[tid];
      data[tid] = data[partner_pos];
      data[partner_pos] = temp;
    }
  }
}

/**
 * CUDA kernel for merging sorted tiles
 * Merges adjacent sorted tiles to create larger sorted sequences
 */
__global__ void bitonicMergeTiles(float* data, int n, int merge_size) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;

  if (tid >= n)
    return;

  // Find which merge group this thread belongs to
  int merge_group = tid / merge_size;
  int pos_in_group = tid % merge_size;
  int merge_start = merge_group * merge_size;

  // Calculate partner position for bitonic merge
  int partner_offset = merge_size / 2;
  int partner_pos;

  if (pos_in_group < partner_offset) {
    partner_pos = merge_start + pos_in_group + partner_offset;
  } else {
    partner_pos = merge_start + pos_in_group - partner_offset;
  }

  // Ensure partner is within bounds
  if (partner_pos >= n || partner_pos >= merge_start + merge_size)
    return;

  // Determine sort direction (ascending for first half, descending for second
  // half)
  bool ascending = (pos_in_group < partner_offset);

  // Perform comparison and swap if necessary
  if ((data[tid] > data[partner_pos]) == ascending) {
    float temp = data[tid];
    data[tid] = data[partner_pos];
    data[partner_pos] = temp;
  }
}

/**
 * CUDA kernel for bitonic sort step in global memory
 * Performs one step of the bitonic sorting network
 */
__global__ void bitonicSortStep(float* data, int j, int k, int n) {
  int i = threadIdx.x + blockDim.x * blockIdx.x;
  int ixj = i ^ j;

  if (ixj > i && i < n && ixj < n) {
    bool ascending = (k & i) == 0;
    if ((data[i] > data[ixj]) == ascending) {
      // Swap elements
      float temp = data[i];
      data[i] = data[ixj];
      data[ixj] = temp;
    }
  }
}

/**
 * Host function to perform complete bitonic sort using global memory
 */
extern "C" void bitonicSort(float* h_data, int n) {
  // Ensure n is a power of 2
  int next_power_of_2 = 1;
  while (next_power_of_2 < n) {
    next_power_of_2 <<= 1;
  }

  float* d_data;
  size_t size = next_power_of_2 * sizeof(float);

  // Allocate device memory
  CUDA_CHECK(cudaMalloc(&d_data, size));

  // Initialize extended array with max values for padding
  float* extended_data = new float[next_power_of_2];
  for (int i = 0; i < n; i++) {
    extended_data[i] = h_data[i];
  }
  for (int i = n; i < next_power_of_2; i++) {
    extended_data[i] = FLT_MAX;
  }

  // Copy data to device
  CUDA_CHECK(cudaMemcpy(d_data, extended_data, size, cudaMemcpyHostToDevice));

  // Calculate grid and block dimensions
  int block_size = 256;
  int grid_size = (next_power_of_2 + block_size - 1) / block_size;

  // Perform bitonic sort
  for (int k = 2; k <= next_power_of_2; k <<= 1) {
    for (int j = k >> 1; j > 0; j >>= 1) {
      bitonicSortStep<<<grid_size, block_size>>>(d_data, j, k, next_power_of_2);
      CUDA_CHECK(cudaGetLastError());
      CUDA_CHECK(cudaDeviceSynchronize());
    }
  }

  // Copy result back to host
  CUDA_CHECK(cudaMemcpy(extended_data, d_data, size, cudaMemcpyDeviceToHost));

  // Copy only the original n elements back
  for (int i = 0; i < n; i++) {
    h_data[i] = extended_data[i];
  }

  // Cleanup
  CUDA_CHECK(cudaFree(d_data));
  delete[] extended_data;
}

/**
 * Host function for shared memory bitonic sort (for smaller arrays)
 */
extern "C" void bitonicSortSharedMemory(float* h_data, int n) {
  // Define maximum tile size for shared memory
  const int TILE_SIZE = 1024;

  // Calculate next power of 2 for the entire array
  int next_power_of_2 = 1;
  while (next_power_of_2 < n) {
    next_power_of_2 <<= 1;
  }

  float* d_data;
  size_t size = next_power_of_2 * sizeof(float);

  // Allocate device memory
  CUDA_CHECK(cudaMalloc(&d_data, size));

  // Create padded array
  float* padded_data = new float[next_power_of_2];
  for (int i = 0; i < n; i++) {
    padded_data[i] = h_data[i];
  }
  for (int i = n; i < next_power_of_2; i++) {
    padded_data[i] = FLT_MAX;
  }

  // Copy to device
  CUDA_CHECK(cudaMemcpy(d_data, padded_data, size, cudaMemcpyHostToDevice));

  if (next_power_of_2 <= TILE_SIZE) {
    // Single tile - use shared memory
    int shared_mem_size = next_power_of_2 * sizeof(float);
    bitonicSortShared<<<1, next_power_of_2, shared_mem_size>>>(
        d_data, next_power_of_2, next_power_of_2);
    CUDA_CHECK(cudaGetLastError());
    CUDA_CHECK(cudaDeviceSynchronize());
  } else {
    // Multiple tiles - use tiled approach
    int num_tiles = next_power_of_2 / TILE_SIZE;

    // Phase 1: Sort individual tiles (all in ascending order)
    int shared_mem_size = TILE_SIZE * sizeof(float);
    bitonicSortShared<<<num_tiles, TILE_SIZE, shared_mem_size>>>(
        d_data, next_power_of_2, TILE_SIZE);
    CUDA_CHECK(cudaGetLastError());
    CUDA_CHECK(cudaDeviceSynchronize());

    // Phase 2: Create bitonic sequence by reversing alternate tiles
    int block_size = 256;
    int grid_size = (next_power_of_2 + block_size - 1) / block_size;
    reverseTiles<<<grid_size, block_size>>>(d_data, next_power_of_2, TILE_SIZE);
    CUDA_CHECK(cudaGetLastError());
    CUDA_CHECK(cudaDeviceSynchronize());

    // Phase 3: Merge tiles using global memory
    for (int k = 2 * TILE_SIZE; k <= next_power_of_2; k <<= 1) {
      for (int j = k >> 1; j > 0; j >>= 1) {
        bitonicSortStep<<<grid_size, block_size>>>(d_data, j, k,
                                                   next_power_of_2);
        CUDA_CHECK(cudaGetLastError());
        CUDA_CHECK(cudaDeviceSynchronize());
      }
    }
  }

  // Copy result back
  CUDA_CHECK(cudaMemcpy(padded_data, d_data, size, cudaMemcpyDeviceToHost));
  for (int i = 0; i < n; i++) {
    h_data[i] = padded_data[i];
  }

  // Cleanup
  CUDA_CHECK(cudaFree(d_data));
  delete[] padded_data;
}

/**
 * CPU implementation for comparison
 */
extern "C" void bitonicSortCPU(float* data, int n) {
  std::sort(data, data + n);
}