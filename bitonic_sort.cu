#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <stdio.h>
#include <algorithm>
#include <cfloat>
#include <cstdlib>

#define CUDA_CHECK(call)                                               \
  do {                                                                 \
    cudaError_t err = call;                                            \
    if (err != cudaSuccess) {                                          \
      fprintf(stderr, "CUDA error at %s:%d: %s\n", __FILE__, __LINE__, \
              cudaGetErrorString(err));                                \
      exit(EXIT_FAILURE);                                              \
    }                                                                  \
  } while (0)

/**
 * CUDA kernel for tiled bitonic sort using shared memory
 * Processes array segments per thread block with proper tiling
 */
__global__ void bitonicSortShared(float* data, int n, int tile_size) {
  extern __shared__ float shared_data[];

  int tid = threadIdx.x;
  int bid = blockIdx.x;
  int block_size = blockDim.x;

  // Calculate the starting position for this tile
  int tile_start = bid * tile_size;
  int tile_end = min(tile_start + tile_size, n);
  int actual_tile_size = tile_end - tile_start;

  // Load tile data into shared memory with proper bounds checking
  for (int i = tid; i < tile_size; i += block_size) {
    if (tile_start + i < tile_end) {
      shared_data[i] = data[tile_start + i];
    } else {
      shared_data[i] = FLT_MAX;  // Padding for power-of-2 requirement
    }
  }
  __syncthreads();

  // Perform bitonic sort on the tile in shared memory
  for (int k = 2; k <= tile_size; k <<= 1) {
    for (int j = k >> 1; j > 0; j >>= 1) {
      for (int i = tid; i < tile_size; i += block_size) {
        int ixj = i ^ j;
        if (ixj > i && ixj < tile_size) {
          bool ascending = (k & i) == 0;
          if ((shared_data[i] > shared_data[ixj]) == ascending) {
            // Swap elements
            float temp = shared_data[i];
            shared_data[i] = shared_data[ixj];
            shared_data[ixj] = temp;
          }
        }
      }
      __syncthreads();
    }
  }

  // Write sorted tile back to global memory
  for (int i = tid; i < actual_tile_size; i += block_size) {
    if (tile_start + i < n) {
      data[tile_start + i] = shared_data[i];
    }
  }
}

/**
 * CUDA kernel for merging sorted tiles
 * Merges adjacent sorted tiles to create larger sorted sequences
 */
__global__ void bitonicMergeTiles(float* data, int n, int merge_size) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;

  if (tid >= n)
    return;

  // Find which merge group this thread belongs to
  int merge_group = tid / merge_size;
  int pos_in_group = tid % merge_size;
  int merge_start = merge_group * merge_size;

  // Calculate partner position for bitonic merge
  int partner_offset = merge_size / 2;
  int partner_pos;

  if (pos_in_group < partner_offset) {
    partner_pos = merge_start + pos_in_group + partner_offset;
  } else {
    partner_pos = merge_start + pos_in_group - partner_offset;
  }

  // Ensure partner is within bounds
  if (partner_pos >= n || partner_pos >= merge_start + merge_size)
    return;

  // Determine sort direction (ascending for first half, descending for second
  // half)
  bool ascending = (pos_in_group < partner_offset);

  // Perform comparison and swap if necessary
  if ((data[tid] > data[partner_pos]) == ascending) {
    float temp = data[tid];
    data[tid] = data[partner_pos];
    data[partner_pos] = temp;
  }
}

__global__ void bitonicMergeKernel(float* data, int n, int j, int k) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;
  int ixj = tid ^ j;

  if (ixj > tid && ixj < n) {
    bool ascending = ((tid & k) == 0);
    if ((data[tid] > data[ixj]) == ascending) {
      float tmp = data[tid];
      data[tid] = data[ixj];
      data[ixj] = tmp;
    }
  }
}

/**
 * Host function for shared memory bitonic sort (for smaller arrays)
 */
extern "C" void bitonicSortSharedMemory(float* h_data, int n) {
  const int MAX_TILE_SIZE = 256;
  int tile_size = MAX_TILE_SIZE;

  // Adjust tile size if array is smaller
  while (tile_size > n && tile_size > 2) {
    tile_size >>= 1;
  }

  float* d_data;
  size_t size = n * sizeof(float);

  // Allocate device memory
  CUDA_CHECK(cudaMalloc(&d_data, size));

  // Copy data to device
  CUDA_CHECK(cudaMemcpy(d_data, h_data, size, cudaMemcpyHostToDevice));

  // Calculate number of tiles needed
  int num_tiles = (n + tile_size - 1) / tile_size;

  // Sort individual tiles using shared memory
  int block_size = min(256, tile_size);  // Threads per block
  int shared_mem_size = tile_size * sizeof(float);

  bitonicSortShared<<<num_tiles, block_size, shared_mem_size>>>(d_data, n,
                                                                tile_size);
  CUDA_CHECK(cudaGetLastError());
  CUDA_CHECK(cudaDeviceSynchronize());

  // Merge tiles using global memory operations
  // Start with merge_size = 2 * tile_size and double each iteration
  for (int k = 2 * tile_size; k <= n; k <<= 1) {
    for (int j = k >> 1; j > 0; j >>= 1) {
      int threads = n;
      int blocks = (threads + 255) / 256;
      bitonicMergeKernel<<<blocks, 256>>>(d_data, n, j, k);
      CUDA_CHECK(cudaGetLastError());
      CUDA_CHECK(cudaDeviceSynchronize());
    }
  }
  // Copy result back to host
  CUDA_CHECK(cudaMemcpy(h_data, d_data, size, cudaMemcpyDeviceToHost));

  // Cleanup
  CUDA_CHECK(cudaFree(d_data));
}

/**
 * CPU implementation for comparison
 */
extern "C" void bitonicSortCPU(float* data, int n) {
  std::sort(data, data + n);
}
