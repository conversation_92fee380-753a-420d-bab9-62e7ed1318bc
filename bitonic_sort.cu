#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <stdio.h>
#include <algorithm>
#include <cfloat>

#define CUDA_CHECK(call)                                               \
  do {                                                                 \
    cudaError_t err = call;                                            \
    if (err != cudaSuccess) {                                          \
      fprintf(stderr, "CUDA error at %s:%d: %s\n", __FILE__, __LINE__, \
              cudaGetErrorString(err));                                \
      exit(EXIT_FAILURE);                                              \
    }                                                                  \
  } while (0)

/**
 * CUDA kernel for bitonic sort using shared memory
 */
__global__ void bitonicSortShared(float* data, int n) {
  extern __shared__ float shared_data[];

  int tid = threadIdx.x;
  int bid = blockIdx.x;
  int block_size = blockDim.x;
  int global_id = bid * block_size + tid;

  // Load data into shared memory
  if (global_id < n) {
    shared_data[tid] = data[global_id];
  } else {
    // Padding with max value because sorting in ascending order
    shared_data[tid] = FLT_MAX;
  }
  __syncthreads();

  // Perform bitonic sort in shared memory
  for (int k = 2; k <= block_size; k <<= 1) {
    for (int j = k >> 1; j > 0; j >>= 1) {
      int ixj = tid ^ j;
      if (ixj > tid && ixj < blockDim.x) {
        bool ascending = (k & tid) == 0;
        if ((shared_data[tid] > shared_data[ixj]) == ascending) {
          // Swap elements
          float temp = shared_data[tid];
          shared_data[tid] = shared_data[ixj];
          shared_data[ixj] = temp;
        }
      }
      __syncthreads();
    }
  }

  // Write back to global memory
  if (global_id < n) {
    data[global_id] = shared_data[tid];
  }
}

/**
 * Host function for shared memory bitonic sort (for smaller arrays)
 */
extern "C" void bitonicSortSharedMemory(float* h_data, int n) {
  int next_power_of_2 = 1;
  while (next_power_of_2 < n) {
    next_power_of_2 <<= 1;
  }

  float* d_data;
  size_t size = next_power_of_2 * sizeof(float);

  // Allocate device memory
  CUDA_CHECK(cudaMalloc(&d_data, size));

  // Prepare extended data
  float* extended_data = new float[next_power_of_2];
  for (int i = 0; i < n; i++) {
    extended_data[i] = h_data[i];
  }
  for (int i = n; i < next_power_of_2; i++) {
    extended_data[i] = FLT_MAX;
  }

  // Copy data to device
  CUDA_CHECK(cudaMemcpy(d_data, extended_data, size, cudaMemcpyHostToDevice));

  // Launch kernel with shared memory
  int shared_mem_size = next_power_of_2 * sizeof(float);
  bitonicSortShared<<<1, next_power_of_2, shared_mem_size>>>(d_data,
                                                             next_power_of_2);
  CUDA_CHECK(cudaGetLastError());
  CUDA_CHECK(cudaDeviceSynchronize());

  // Copy result back
  CUDA_CHECK(cudaMemcpy(extended_data, d_data, size, cudaMemcpyDeviceToHost));

  // Copy only original elements back
  for (int i = 0; i < n; i++) {
    h_data[i] = extended_data[i];
  }

  // Cleanup
  CUDA_CHECK(cudaFree(d_data));
  delete[] extended_data;
}

/**
 * CPU implementation for comparison
 */
extern "C" void bitonicSortCPU(float* data, int n) {
  std::sort(data, data + n);
}
