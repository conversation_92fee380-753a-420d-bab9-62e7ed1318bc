echo "Building BitonicSortGPU..."

# Check if CUDA is available
if ! command -v nvcc &> /dev/null; then
    echo "Error: nvcc not found"
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: python3 not found"
    exit 1
fi

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Build the CUDA extension
echo "Building CUDA extension..."
python3 setup.py build_ext --inplace

if [ $? -eq 0 ]; then
    echo "Build successful!"
    echo "You can now run: python3 main.py"
else
    echo "Build failed!"
    exit 1
fi
