#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <chrono>
#include <vector>

// Forward declarations of CUDA functions
extern "C" void bitonicSortSharedMemory(float* data, int n);
extern "C" void bitonicSortCPU(float* data, int n);

namespace py = pybind11;

/**
 * Python wrapper for GPU bitonic sort with shared memory
 */
py::array_t<float> bitonic_sort_gpu_shared(py::array_t<float> input) {
  py::buffer_info buf_info = input.request();

  if (buf_info.ndim != 1) {
    throw std::runtime_error("Input array must be 1-dimensional");
  }

  int n = buf_info.shape[0];
  float* input_ptr = static_cast<float*>(buf_info.ptr);

  // Create output array
  auto result = py::array_t<float>(n);
  py::buffer_info result_buf = result.request();
  float* result_ptr = static_cast<float*>(result_buf.ptr);

  // Copy input to result
  std::copy(input_ptr, input_ptr + n, result_ptr);

  // Perform GPU bitonic sort with shared memory
  bitonicSortSharedMemory(result_ptr, n);

  return result;
}

/**
 * Python wrapper for CPU bitonic sort (for comparison)
 */
py::array_t<float> bitonic_sort_cpu(py::array_t<float> input) {
  py::buffer_info buf_info = input.request();

  if (buf_info.ndim != 1) {
    throw std::runtime_error("Input array must be 1-dimensional");
  }

  int n = buf_info.shape[0];
  float* input_ptr = static_cast<float*>(buf_info.ptr);

  // Create output array
  auto result = py::array_t<float>(n);
  py::buffer_info result_buf = result.request();
  float* result_ptr = static_cast<float*>(result_buf.ptr);

  // Copy input to result
  std::copy(input_ptr, input_ptr + n, result_ptr);

  // Perform CPU sort
  bitonicSortCPU(result_ptr, n);

  return result;
}

/**
 * Timed version of GPU bitonic sort with shared memory
 */
std::pair<py::array_t<float>, double> bitonic_sort_gpu_shared_timed(
    py::array_t<float> input,
    int num_iterations) {
  double total_time_us = 0.0;
  py::array_t<float> result;

  for (int i = 0; i < num_iterations; ++i) {
    auto input_copy = py::array_t<float>(input);
    auto start = std::chrono::high_resolution_clock::now();
    result = bitonic_sort_gpu_shared(input_copy);
    auto end = std::chrono::high_resolution_clock::now();
    total_time_us +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  }

  double average_ms = total_time_us / num_iterations / 1000.0;
  return std::make_pair(result, average_ms);
}

/**
 * Timed version of CPU bitonic sort
 */
std::pair<py::array_t<float>, double> bitonic_sort_cpu_timed(
    py::array_t<float> input,
    int num_iterations) {
  double total_time_us = 0.0;
  py::array_t<float> result;

  for (int i = 0; i < num_iterations; ++i) {
    auto input_copy = py::array_t<float>(input);
    auto start = std::chrono::high_resolution_clock::now();
    result = bitonic_sort_cpu(input_copy);
    auto end = std::chrono::high_resolution_clock::now();
    total_time_us +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  }

  double average_ms = total_time_us / num_iterations / 1000.0;
  return std::make_pair(result, average_ms);
}

PYBIND11_MODULE(bitonic_sort_cuda, m) {
  m.doc() = "Bitonic Sort CUDA implementation with Python bindings";

  m.def("bitonic_sort_gpu_shared", &bitonic_sort_gpu_shared,
        "Sort array using GPU bitonic sort with shared memory",
        py::arg("input"));

  m.def("bitonic_sort_cpu", &bitonic_sort_cpu,
        "Sort array using CPU implementation", py::arg("input"));

  m.def("bitonic_sort_gpu_shared_timed", &bitonic_sort_gpu_shared_timed,
        py::arg("input"), py::arg("num_iterations") = 100000);

  m.def("bitonic_sort_cpu_timed", &bitonic_sort_cpu_timed, py::arg("input"),
        py::arg("num_iterations") = 100000);
}
